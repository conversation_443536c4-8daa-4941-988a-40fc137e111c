2025-06-15 00:40:09,141 - SimpleInterface - ERROR - AI response error: 'NoneType' object has no attribute 'generate_response'
2025-06-15 00:41:07,433 - <PERSON><PERSON><PERSON> - INFO - Initializing Gideon Core System...
2025-06-15 00:41:07,437 - MemorySystem - INFO - Loaded 17 recent conversations
2025-06-15 00:41:07,438 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-15 00:41:07,438 - MemorySystem - INFO - Memory system initialized successfully
2025-06-15 00:41:07,439 - ModelManager - INFO - No existing models config found
2025-06-15 00:41:07,440 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-15 00:41:07,440 - AIEngine - INFO - 🌍 AI Engine default response language set to: ar
2025-06-15 00:41:07,442 - AIEngine - INFO - Loaded 30 learned response patterns
2025-06-15 00:41:07,442 - AIEngine - INFO - Initializing LLM backends...
2025-06-15 00:41:07,442 - AIEngine - INFO - ✅ Ollama backend available
2025-06-15 00:41:07,443 - AIEngine - INFO - ℹ️ llama.cpp backend not available
2025-06-15 00:41:07,443 - AIEngine - INFO - ℹ️ CTransformers backend not available
2025-06-15 00:41:07,443 - AIEngine - INFO - ✅ Transformers backend available
2025-06-15 00:41:07,443 - AIEngine - INFO - Initialized 2 LLM backends
2025-06-15 00:41:07,444 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-15 00:41:07,704 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-15 00:41:07,705 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-15 00:41:07,705 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-15 00:41:07,706 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-15 00:41:07,707 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-15 00:41:07,707 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-15 00:41:07,707 - AIEngine - INFO - AI Engine initialized successfully
2025-06-15 00:41:07,842 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (DualSense W', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Speakers (DualSense Wireless Co', 'Headset Earphone (Razer Nari Ul', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Primary Sound Capture Driver', 'Headset Microphone (DualSense Wireless Controller)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (DualSense Wireless Controller)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Speakers (DualSense Wireless Controller)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (7.1 Surround Sound)', 'Headset Microphone (DualSense Wireless Controller)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Speakers (DualSense Wireless Controller)', 'Headset Microphone (DualSense Wireless Controller)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-15 00:41:07,907 - STTEngine - INFO - 🎤 Selected preferred microphone: Headset Microphone (DualSense W
2025-06-15 00:41:08,047 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-15 00:41:08,047 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-15 00:41:11,066 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 9749.700694329327
2025-06-15 00:41:11,084 - STTEngine - INFO - 🔧 Recognizer configured:
2025-06-15 00:41:11,084 - STTEngine - INFO -    Energy threshold: 9749.700694329327
2025-06-15 00:41:11,085 - STTEngine - INFO -    Pause threshold: 0.8s
2025-06-15 00:41:11,085 - STTEngine - INFO -    Phrase threshold: 0.3s
2025-06-15 00:41:11,085 - STTEngine - INFO - 🎯 Wake word variants initialized: 10 English, 7 Arabic
2025-06-15 00:41:11,085 - STTEngine - INFO - 🌍 Auto language detection enabled (bilingual mode)
2025-06-15 00:41:11,085 - STTEngine - INFO - ✅ Enhanced speech recognition initialized successfully
2025-06-15 00:41:11,085 - STTEngine - INFO - 🌍 Bilingual mode: True
2025-06-15 00:41:11,086 - STTEngine - INFO - 🎯 Supported languages: ['en-US', 'ar-SA', 'ar-EG', 'ar-AE']
2025-06-15 00:41:11,348 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-15 00:41:11,348 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-15 00:41:11,348 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-15 00:41:14,627 - STTEngine - INFO - Testing microphone... Say something!
2025-06-15 00:41:20,649 - STTEngine - WARNING - Microphone test: No speech recognized
2025-06-15 00:41:24,853 - TerminalManager - INFO - Created default terminal session: session_0
2025-06-15 00:41:24,854 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-15 00:41:24,855 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-15 00:41:24,855 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-15 00:41:42,320 - STTEngine - ERROR - Unexpected recognition error for ar-SA: [WinError 2] The system cannot find the file specified
2025-06-15 00:42:23,255 - STTEngine - INFO - 🌍 Detected language: ar | Text: 'ما حدا فيكم حكى لي مرحبا' | Confidence: 0.87
2025-06-15 00:42:23,260 - STTEngine - INFO - 🎤 Heard: 'ما حدا فيكم حكى لي مرحبا'
2025-06-15 00:42:40,986 - STTEngine - INFO - 🌍 Detected language: ar | Text: 'احنا في زمن مسخره' | Confidence: 0.79
2025-06-15 00:42:40,987 - STTEngine - INFO - 🎤 Heard: 'احنا في زمن مسخره'
2025-06-15 00:42:45,608 - STTEngine - INFO - 🌍 Detected language: ar | Text: 'صبركم علي في المسخره' | Confidence: 0.81
2025-06-15 00:42:45,608 - STTEngine - INFO - 🎤 Heard: 'صبركم علي في المسخره'
2025-06-15 00:42:55,467 - AIEngine - INFO - Generating response for: 'هلا'
2025-06-15 00:42:55,470 - AIEngine - INFO - Attempting LLM response generation...
2025-06-15 00:42:55,470 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-15 00:42:55,470 - AIEngine - INFO - 🎯 Using explicitly set response language: ar
2025-06-15 00:42:55,471 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-15 00:42:55,471 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-15 00:43:32,435 - STTEngine - INFO - 🌍 Detected language: ar | Text: 'شكو' | Confidence: 0.90
2025-06-15 00:43:32,435 - STTEngine - INFO - 🎤 Heard: 'شكو'
2025-06-15 00:44:14,113 - AIEngine - INFO - Generating response for: 'سلام'
2025-06-15 00:44:14,113 - AIEngine - INFO - Attempting LLM response generation...
2025-06-15 00:44:14,114 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-15 00:44:14,114 - AIEngine - INFO - 🎯 Using explicitly set response language: ar
2025-06-15 00:44:14,114 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-15 00:44:14,115 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-15 00:44:39,144 - AIEngine - INFO - LLM response generated successfully: مرحباً، كيف يمكنني مساعدتك اليوم؟...
2025-06-15 00:44:39,145 - AIEngine - INFO - LLM generated successful response: مرحباً، كيف يمكنني مساعدتك اليوم؟...
2025-06-15 00:44:39,171 - AIEngine - INFO - Final response generated: مرحباً، كيف يمكنني مساعدتك اليوم؟...
2025-06-15 00:44:55,208 - AIEngine - INFO - LLM response generated successfully: مرحباً، كيف يمكنني مساعدتك اليوم؟...
2025-06-15 00:44:55,208 - AIEngine - INFO - LLM generated successful response: مرحباً، كيف يمكنني مساعدتك اليوم؟...
2025-06-15 00:44:55,212 - AIEngine - INFO - Final response generated: مرحباً، كيف يمكنني مساعدتك اليوم؟...
